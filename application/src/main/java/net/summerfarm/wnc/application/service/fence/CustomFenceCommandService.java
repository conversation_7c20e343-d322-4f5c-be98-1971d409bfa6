package net.summerfarm.wnc.application.service.fence;

import net.summerfarm.wnc.application.inbound.controller.fence.Input.command.AddCityAreaChangeWarehouseRecordCommandInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.command.AddCustomFenceAreaCommandInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.command.AddCustomFenceChangeRecordCommandInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.command.UpdateCustomFenceChangeRecordCommandInput;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.AddCustomFenceAreaVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.AddCustomFenceChangeRecordVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CityAreaChangeWarehouseRecordAddVO;

/**
 * Description: <br/>
 * date: 2025/8/26 17:29<br/>
 *
 * <AUTHOR> />
 */
public interface CustomFenceCommandService {

    /**
     * 新增自定义区域变更记录
     * @param input 新增参数
     * @return 结果
     */
    CityAreaChangeWarehouseRecordAddVO addCityAreaChangeWarehouseRecord(AddCityAreaChangeWarehouseRecordCommandInput input);

    /**
     * 新增自定义围栏变更记录
     * @param input 新增参数
     * @return 结果
     */
    AddCustomFenceChangeRecordVO addCustomFenceChangeRecord(AddCustomFenceChangeRecordCommandInput input);

    /**
     * 新增自定义围栏区域
     * @param input 新增参数
     * @return 结果
     */
    AddCustomFenceAreaVO addCustomFenceArea(AddCustomFenceAreaCommandInput input);

    /**
     * 更新自定义围栏变更记录
     * @param input 更新参数
     * @return 是否成功
     */
    boolean updateCustomFenceChangeRecord(UpdateCustomFenceChangeRecordCommandInput input);
}
